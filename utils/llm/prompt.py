import json
import os.path
import warnings

from openai import OpenAI
from PIL import Image
from dotenv import load_dotenv
from utils.logger.logger import Logging

logger = Logging().get_logger()
load_dotenv()


def analize_check_points_prompt(require):
    """
    分析需求文档中的测试点
    :param require: 需求文档字符串
    :return: 检查点列表
    """
    prompt=f"""
# 任务：帮我拆分下面PRD 的功能测试点,对于每个功能，尽可能全面的拆分出测试点，要保证 PRD 里提到的所有点都覆盖到。
# 需求文档 PRD如下:
{require}
    """
    return prompt


def check_case_prompt(require,usecase,check_points):
    prompt=f"""
# 你是一个测试工程师
# 任务：根据PRD 产品需求文档和功能模块测试点，判断当前用例集是否有遗漏测试点，有则输出。

# PRD 需求文档：
{require}
# 功能模块测试点：
{check_points}
————————————————————

# 待评审测试用例集：
```json
{usecase}
```
————————————————————
# 输出：
只需要输出.csv 格式的用例缺失清单，不0需要其他解释。
## 输出示例：
维度,覆盖缺失,优先级
功能覆盖,挂号功能未覆盖患者未登录状态下挂号的场景,P0
功能覆盖,在线问诊未覆盖医生超时未响应的场景,P1
功能覆盖,电子处方未覆盖处方审核失败的场景,P1
功能覆盖,健康档案管理未覆盖患者信息更新失败的场景,P1
数据覆盖,患者年龄未覆盖小于18岁或大于120岁的边界值场景,P1
数据覆盖,挂号信息未覆盖重复挂号信息的场景,P1
数据覆盖,药品库存未覆盖库存为0或负值的场景,P1
场景覆盖,挂号功能未覆盖患者使用多种挂号类型（如普通号、专家号）的场景,P1
场景覆盖,在线问诊未覆盖问诊中断后重新连接的场景,P1
场景覆盖,药品配送未覆盖配送地址为空的场景,P1
    """
    return prompt


def review_prompt(usecase, require):
    prompt=f"""
## 角色设定
你是一位资深的高级测试工程师，擅长从产品需求文档中找出当前用例未覆盖的场景。
## 核心任务
根据产品需求文档，验证测试用例的完整性和有效性，识别覆盖缺口和潜在风险，并输出结构化的用例评审结果。

## 评审维度
请从以下维度对测试用例进行评审：

### 功能覆盖
• ​**业务功能完整性**：测试用例是否覆盖核心业务功能点。
• ​**异常处理能力**：测试用例是否覆盖异常情况下的处理逻辑。

### 数据覆盖
• ​**边界值测试**：测试用例是否覆盖了数值型字段的边界值。
• ​**异常数据测试**：测试用例是否覆盖了非法数据、空值、重复值等异常输入。
• ​**数据组合测试**：测试用例是否覆盖了多字段组合输入的场景。

### 场景覆盖
• ​**正常场景**：测试用例是否覆盖了用户正常操作的典型场景）。
• ​**异常场景**：测试用例是否覆盖了用户操作中的异常场景。
• ​**混合场景**：测试用例是否覆盖了多个功能模块交互的复杂场景。

## 输入数据
### 产品需求文档
```
{require}
```

### 待评审测试用例集
```
{usecase}
```

## 输出要求
### 输出结构
请按照以下结构输出用例评审结果：

#### 1. 结果以.csv 格式文本输出，只需要输出markdown格式的用例缺失清单，不需要其他解释。

#### 缺失清单描述要求：
  • 列出所有可能的覆盖缺失项，按维度分类，每个维度的缺失项需包括以下字段：
  • **覆盖缺失**：具体的未覆盖场景描述。
  • **需求描述**：对应的需求点。
  • **缓解措施**：针对覆盖缺陷，建议编写的用例。
  • **优先级**：问题的优先级（P0/P1/P2/P3）。
---

## 输出示例：
维度,覆盖缺失,需求描述,缓解措施,优先级
功能覆盖,挂号功能未覆盖患者未登录状态下挂号的场景,患者未登录状态下挂号的场景,补充未登录状态下的挂号测试用例,P0
功能覆盖,在线问诊未覆盖医生超时未响应的场景,医生超时未响应的场景,补充医生超时未响应的测试用例,P1
功能覆盖,电子处方未覆盖处方审核失败的场景,处方审核失败的场景,补充处方审核失败的测试用例,P1
功能覆盖,健康档案管理未覆盖患者信息更新失败的场景,患者信息更新失败的场景,补充患者信息更新失败的测试用例,P1
数据覆盖,患者年龄未覆盖小于18岁或大于120岁的边界值场景,患者年龄小于18岁或大于120岁的场景,补充边界值测试用例,P1
数据覆盖,挂号信息未覆盖重复挂号信息的场景,重复挂号信息的场景,补充重复输入的测试用例,P1
数据覆盖,药品库存未覆盖库存为0或负值的场景,药品库存为0或负值的场景,补充库存边界值测试用例,P1
场景覆盖,挂号功能未覆盖患者使用多种挂号类型（如普通号、专家号）的场景,患者使用多种挂号类型的场景,补充多挂号类型组合的测试用例,P1
场景覆盖,在线问诊未覆盖问诊中断后重新连接的场景,问诊中断后重新连接的场景,补充问诊中断恢复的测试用例,P1
场景覆盖,药品配送未覆盖配送地址为空的场景,配送地址为空的场景,补充配送地址为空的测试用例,P1
    """
    return prompt


# 从文档内容中提取标题的函数
def extract_document_title(require):
    """
    从文档内容中提取标题
    参数:
        content: 包含标题的文档内容字符串
    返回:
        提取到的标题字符串
    """
    prompt=f"""
# 任务：针对下面需求文档，按照文档结构和需求中的功能模块，尽可能详细的划分文档，输出文档模块界限标题（完全来源于文档，不需要包含该模块内容）。 
# 需求文档：
{require}
# 输出要求：
1.以 json格式输出，其中每个元素为一个模块界限标题;
2.输出的标题应该完全来源于文档（精确到每个字符）；
3.不需要其他解释
# 输出示例：
["模块1标题","模块2标题",......]
    """
    return  prompt


def format_require_prompt(require):
    prompt=f"""
# 任务：下面是一个需求文档中某个模块的内容，需要提取内容中的图片链接，格式化内容，返回一个 json ，包含 title,content,img_urls：图片列表。
# 输入内容：
{require}
# 输出要求：
1. 以 json格式输出;
2. 包含以下字段：title,content,img_urls;
3. 不需要其他解释
# 输出示例：
{{
   "title":"",  //模块标题
   "content":"",  //模块内容
   "img_urls":[]  //模块中所有图片链接
}}
"""
    return prompt

def is_need_gen_case(require):
    prompt=f"""
# 任务：下面模块内容属于需求文档中某一段落的内容，根据是否属于不需要编写测试用例的场景，判断是否需要编写测试用例。
# 模块内容如下：
{require}

# 不需要编写测试用例的场景如下：
1. 当内容不涉及功能实现；
2. 当内容是简单无意义的描述；
3. 关于需求背景的描述；
4、关于埋点、日志、监控等的描述；
5、关于数据上报的描述；

# 输出：当需要编写测试用例时，输出是，否则输出否
    """
    return  prompt


def gen_case_prompt(require):
    prompt=f"""
# 核心任务：分析产品需求文档功能模块内容和图片（若有），编写详细的测试用例集。
# 产品需求文档功能模块描述：
——————
{require}
——————
# 输出要求：
1.以markdown格式输出测试用例，不需要其他解释；
2.按照下面格式输出：
# 目录标题：            //概述该功能模块描述
## 子目录标题：     //概述该模块子功能描述
### 用例标题：      //测试用例标题描述，要求
  - 预期结果：  //预期结果描述
### 用例标题：
  - 预期结果：

## 子目录标题：
### 用例标题：
  - 预期结果：
### 用例标题：
  - 预期结果：
...

# 输出示例：
# 目录标题：概述该功能模块
## 子目录标题：概述该模块子功能
### 用例标题：测试用例标题
  - 预期结果：预期结果描述

"""
    return prompt

def case_level_prompt(use_case_title):
    prompt=f"""
# 测试用例如下：
{use_case_title}    
# 任务：上面是一个测试点列表，按照下面规则分析每个测试用例的优先级，返回一个测试点优先级list，用例优先级；
# 规则：
（1）高：属于系统基本功能，该测试点执行失败会导致多处重要功能无法运行；
（2）较高：属于系统的重要功能，主要包括一些功能交互相关、各种应用场景、使用频率较高的正常功能测试用例；
（3）低：属于系统的一般功能，使用频率较低，如特殊字符，字符串超长，消息超时等场景；
# 输出要求：
1.  输出一个json 结构列表,包含每个用例的优先级，不需要其他解释
# 输出示例：
[
    {{
        "title": "测试用例标题",
        "level": "高"
    }},
    {{
        "title": "测试用例标题",
        "level": "较高"
    }},
    {{
        "title": "测试用例标题",
        "level": "低"
    }}
    ...
]
"""
    return prompt

def single_case_level_prompt(use_case_title):
    prompt=f"""
 
# 任务：根据下面规则分析测试用例的优先级，输出优先级；
# 测试用例如下：
{use_case_title}   
# 规则：
（1）#0：属于系统基本功能，该用例执行失败会导致多处重要功能无法运行，输出#0；
（2）#1：属于系统的重要功能，主要包括一些功能交互相关、各种应用场景、使用频率较高的正常功能测试用例，输出#1；
（3）#2：属于系统的一般功能，使用频率较低，如特殊字符，字符串超长，消息超时等场景；
# 输出要求：
1. 直接输出优先级等级，#0 或者 #1 或者 #2，不需要其他解释
# 输出示例：
#1
"""
    return prompt


def gen_story_core_prompt(story):
    prompt=f"""
# 角色​​：你是一个资深产品经理，擅长从复杂文档中提炼关键信息。你需要用技术文档解析和产品思维两种能力协同工作。

​# 核心​任务​​：分析以下产品需求文档(PRD)，根据规则提取核心功能测试点。
# 产品需求文档(PRD)：
{story}

# ​规则​：
- 直接影响用户核心体验的功能（如注册流程）
- 支撑主要业务场景的必备功能（如支付模块）
- 需要跨系统联调的接口功能（如第三方登录）
- 技术实现复杂度≥3级的功能（按NASA标准）

# 输出格式
输出一个 list，包含每个核心功能测试点。
每个核心功能点需要是包含主语的单一功能测试点。
# 输出示例：
[核心功能点测试点1, 核心功能点测试点2, 核心功能点测试点3, ...]

  """
    return prompt


def extract_summary_prompt(require):
    """
    从产品需求文档中提取摘要
    :param require: 需求文档内容字符串
    :return: 提取摘要的 prompt
    """
    prompt=f"""
# 任务：从以下产品需求文档中提取核心摘要，突出核心功能和关键点,过滤掉埋点和数据上报相关内容。
# 需求文档内容：
{require}

# 输出要求：
1. 摘要应包含需求的核心功能、目标用户和关键业务场景。
2. 用简洁的段落描述，避免冗余信息。
3. 不需要包含技术实现细节或非核心内容。
4. 摘要不应包含任何埋点、数据上报相关内容。

# 示例输出：
本需求旨在实现XX功能，核心业务场景包括XX、XX和XX。关键功能点包括XX、XX和XX。
    """
    return prompt

def gen_function_modules_prompt(story):
    """
    根据产品需求文档生成功能模块的 prompt
    :param story: 产品需求文档内容
    :return: 生成功能模块的 prompt
    """
    prompt = f"""
### 角色
您作为资深产品经理，需要从复杂需求中识别核心功能模块，排除非核心和技术性功能。

### 任务说明
1. 仔细分析以下产品需求文档
2. 提取所有**核心业务功能模块**
3. 每个模块必须包含：
   - 模块名称（反映核心业务价值）
   - 功能描述（需说明所属页面/模块归属）

### 需求文档
{story}

### 强制约束
- ⛔ 排除：埋点管理、数据上报、日志监控、技术支撑类功能
- ✋ 排除：用户未明确要求的隐含功能
- 🔍 描述必须体现模块定位（例："在购物车页面处理商品增删"）
- 📦 每个模块必须是独立可交付的业务单元

### 输出格式
[
  {{
    "name": "模块名称", // name 尽可能描述的详细一点。
    "description": "在[页面/模块]中，实现...[功能说明]"
  }},
  // 其他模块
]

### 示例
[
  {{
    "name": "订单支付",
    "description": "在结账页面处理支付方式选择、交易执行及结果反馈"
  }},
  {{
    "name": "商品推荐",
    "description": "在首页推荐模块，基于用户行为生成个性化商品列表"
  }}
]

请严格遵循输出格式，不要包含任何额外解释文本。
"""
    return prompt

# def gen_function_modules_prompt(story):
#     """
#     根据产品需求文档生成功能模块的 prompt
#     :param summary: 产品需求摘要字符串
#     :return: 生成功能模块的 prompt
#     """
#     prompt=f"""
# # 角色：你是一个资深产品经理，擅长从复杂的产品需求文档中提取核心功能模块。
# # 任务：从以下产品需求文档中提取核心功能模块，生成每个核心功能模块的名称和简要功能描述，功能描述需要有全局意识，每一个功能模块最好带上所在的页面或者模块归属。
# # 产品需求文档：
# {story}

# # 输出要求：
# 1. 每个功能模块需包含模块名称和简要功能描述。
# 2. 不生成与埋点管理、数据上报相关的模块。
# 3. 输出格式为 JSON 列表，每个功能模块为一个对象，包含 `name` 和 `description` 字段,不需要其他解释。

# # 示例输出：
# [
#     {{
#         "name": "用户管理",
#         "description": "负责用户注册、登录、权限管理等功能。"
#     }},
#     {{
#         "name": "支付",
#         "description": "处理订单支付、退款、对账等支付相关功能。"
#     }}
# ]
#     """
#     return prompt

def polish_function_modules_prompt(story,modules):
    """
    根据产品需求文档和功能模块列表，润色功能模块的 prompt
    :param story: 产品需求文档内容字符串
    :param modules: 功能模块列表（JSON 格式字符串）
    :return: 润色功能模块的 prompt
    """
    prompt = f"""
# 任务：根据以下产品需求文档和功能模块列表，输出润色后的模块。
# 润色要求：
1.识别功能所属模块，页面，在description中体现；
2.不需要过于细节的描述，主要描述核心功能场景。
# 需求文档内容：
{story}
# 功能模块列表：
{modules}
# 输出要求：
1. 返回一个 JSON 列表，仅包含润色后的功能模块。
2. 每个模块对象包含 `name` 和 `description` 字段。
3. 不需要其他解释。
# 示例输出：
[
    {{
        "name": "会议详情分Tab展示",
        "description": "将会议详情页的内容按照不同类别分Tab展示，包括合规看板、参会人信息、议题信息、费用与预算、相关资料、转写与纪要等，提升信息查看的清晰度和效率。"
    }},
    {{
        "name": "基础信息展示",
        "description": "在会议详情页的基础信息tab展示会议标题、状态、外部参会人数、内部参会人数、审批状态、相关产品及会议地址等信息，并支持查看审批节点详情。"
    }},
    {{
        "name": "参会人信息管理",
        "description": "在会议详情页的参会人信息Tab中展示讲者、参会人、内部参会人、执行人、协办人等列表，包含计数、字段展示及操作栏功能，如确认使用情况、下载邀请函、添加机酒等。"
    }},
]
"""
    return prompt

def filter_relevant_modules_prompt(require, modules):
    """
    根据产品需求文档和功能模块列表，筛选相关的核心模块
    :param require: 产品需求文档内容字符串
    :param modules: 功能模块列表（JSON 格式字符串）
    :return: 筛选相关模块的 prompt
    """
    prompt = f"""
# 任务：根据以下产品需求文档和功能模块列表，筛选出与需求最核心相关的功能模块列表。
# 需求文档内容：
{require}

# 功能模块列表：
{modules}

# 筛选规则：
1. 核心模块必须直接支持需求文档中描述的核心功能或业务场景。
2. 排除以下模块：
   - 非核心功能或边缘场景的模块。
   - 功能描述模糊或无法与需求文档对应的模块。
   - 与需求主题无关的模块。
   - 与埋点管理、数据上报相关的模块。

# 输出要求：
1. 返回一个 JSON 列表，仅包含核心相关的功能模块。
2. 每个模块对象包含 `name` 和 `description` 字段
3. 不需要解释筛选过程。

# 示例输出：
[
    {{
        "name": "用户管理",
        "description": "负责用户注册、登录、权限管理等核心功能。"
    }},
    {{
        "name": "支付",
        "description": "处理订单支付、退款等核心业务场景。"
    }}
]
"""
    return prompt

def find_modules_prompt(core, modules):
    """
    根据查询内容在功能模块列表中查找相关模块
    :param core: 核心功能
    :param modules: 功能模块列表（JSON 格式字符串）
    :return: 查找相关模块的 prompt
    """
    prompt = f"""
    # 任务：对于以下功能描述，在功能模块列表中查找最相关模块，返回最相关的模块列表。
    # 功能描述：
    {core}
    # 功能模块列表：
    {modules}
    # 输出要求：
    1. 返回一个 list 列表，仅包含最相关的模块。
    2. 相关的模块可能有一个或多个。
    3. 直接输出模块列表，不需要解释。
    # 示例输出：
    ["最相关的模块1", "最相关的模块2", ...]
    """
    return prompt

def extract_module_from_story_prompt(story):
    """
    从产品需求文档中提取功能模块
    :param story: 产品需求文档内容字符串
    :return: 提取功能模块和图片链接的 prompt
    """
    prompt=f"""
# 任务：从以下产品需求文档中提取每个主要功能模块的详细描述和每个功能模块下的图片链接。
# 产品需求文档：
{story}
# 输出要求：
1.不提取数据上报和埋点相关的内容。
2. 返回一个 JSON 列表，每个对象包含`name`字段， `description` 字段，`img_urls` 字段包含图片链接。
3. name为模块名称，description 字段为模块涉及到的主要功能的总结，img_urls 字段为图片链接列表。
4. 不需要其他解释。
# 示例输出：

    {{
        "description": "负责用户注册、登录、权限管理等核心功能。",
        "img_urls": ["http://example.com/image1.png", "http://example.com/image2.png"]
    }},
    {{
        "description": "处理订单支付、退款等核心业务场景。",
        "img_urls": ["http://example.com/image3.png", "http://example.com/image4.png"]
    }}
]
    """
    return prompt

# def check_query_relevant_module_prompt(query,modules):
#     """
#     检查模块目录是否与查询相关
#     :param query: 查询内容字符串
#     :param modules: 功能模块列表（JSON 格式字符串）
#     :return: 检查相关性的 prompt
#     """
#     prompt = f"""
# # 任务：现在有一批测试用例的模块目录，根据需求描述和测试用例模块目录列表，识别可能受本次需求变更影响的模块目录列表（需回归测试）。
# # 需求描述：
# {query}

# # 目录模块列表（排序越靠前的越有可能相关）：
# {modules}

# # 注意：识别当前需求涉及的功能测试用例可能存放在哪些目录下，可能通过关键词或者完整语义识别，不要错过可能的模块列表。

# # 输出要求：
# 1. 从功能模块列表中挑选可能需要回归测试的模块目录列表。
# 2. 选择的模块目录可能有0 个、一个或多个，注意只要觉得有可能相关，就可以选择。
# 3. 直接输出需要回归测试的模块目录列表JSON 数组，不需要其他解释，可以参考下面例子。

# # 示例
# ## 输入：
# ### 需求描述：
#   {{
#     "name": "会议提交审批校验",
#     "description": "在会议提交审批页面，对接入合规引擎进行校验，包括活动时间、活动日期、活动时长、跨天限制、参会人数、活动地点、讲者、会议材料、预算与费用、会议次数等方面的合规性校验"
#   }},
# ### 目录模块列表：
# ['会议模块/管理端/审批中心', '会议模块/合规看板/管理端', '会议模块/合规看板/小程序端', '会议模块/会议详情/小程序端/审批中状态', '会议模块/会议详情/管理端/审批中状态', '会议模块/会议详情/管理端/待提交状态', '会议模块/会议详情/小程序端/待提交状态']

# ## 输出：
# [会议模块/合规看板/小程序端，会议模块/合规看板/管理端]
#     """
    
#     return prompt


def check_query_relevant_module_prompt(query,modules):
    """
    检查模块目录是否与需求相关，可能需要回归测试
    :param query: 查询内容字符串
    :param modules: 功能模块列表（JSON 格式字符串）
    :return: 检查相关性的 prompt
    """
    prompt = f"""
# 任务：检查功能模块目录是否与查询内容相关，保留可能相关的模块目录。
# 查询内容：
{query}
# 功能模块目录列表（排序越靠前的越有可能相关）：
{modules}
# 输出要求：
1.直接输出可能相关的模块目录列表JSON 数组，不需要其他解释。
2. 选择的模块目录可能有0 个、一个或多个，注意只要觉得有可能相关，就可以选择。

# 示例
["相关目录模块1","相关目录模块2", "相关目录模块3"]
    """
    return prompt

def polish_story_prompt(story):
    """
    对产品需求文档进行润色
    :param story: 产品需求文档内容字符串
    :return: 润色后的产品需求文档的 prompt
    """
    prompt = f"""
# 任务：对以下产品需求文档进行润色,按照以下润色要求，输出一个润色后的产品需求文档。
# 润色要求：
1. 删除需求中的链接，无效字符，保持文档简洁。
2. 删除关于埋点和数据上报的内容。
3. 确保文档结构清晰，逻辑连贯。

# 产品需求文档：
{story}

# 输出要求：
1. 返回润色后的产品需求文档字符串。
2. 不需要其他解释。
"""
    return prompt

def rag_for_story_prompt(story,rag_contents):
    """
    使用 RAG 召回历史相关需求和业务文档，补充需求理解
    :param story: 产品需求文档内容字符串
    :return: RAG 召回的 prompt
    """
    prompt = f"""
    # 任务：从参考资料中挑选相关的内容，补充对以下产品需求文档的理解。
    # 参考资料：
    {rag_contents}
    # 产品需求文档：
    {story} 
    # 输出要求：
    1.输出一个补充理解后的产品需求文档字符串。
    2.不需要其他解释。
    """
    return prompt
    
    