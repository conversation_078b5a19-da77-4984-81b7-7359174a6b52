import pandas as pd # type: ignore
import gradio as gr # type: ignore
import pandas as pd # type: ignore
from pandas import DataFrame # type: ignore
import re
import ast
import time
from openai import OpenAI # type: ignore
import json
import os.path
from dotenv import load_dotenv # type: ignore
from io import StringIO
from datetime import datetime
import os
from utils.logger.logger import Logging
from utils.llm.chat import chat_stream, hunyuan_vision
from utils.llm.prompt import extract_summary_prompt,gen_function_modules_prompt,filter_relevant_modules_prompt,find_modules_prompt,extract_module_from_story_prompt,polish_function_modules_prompt
from utils.dify import DifyClient
from utils.tapd import TAPDUtils
from utils.iwiki import IwikiToMdConverter

logger = Logging().get_logger()
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_t1_key'))
load_dotenv()

def gen_case_path(require_name):
    case_dir = "data/regress_cases"
    # 生成日期子目录（格式为YYYYMMDD）
    date_subdir = datetime.now().strftime("%Y%m%d")
    case_dir = os.path.join(case_dir, date_subdir)
    
    # 确保输出目录存在
    os.makedirs(case_dir, exist_ok=True)
    
    # 替换文件名中的特殊字符
    safe_name = re.sub(r'[\\/:*?"<>|]', '_', require_name)  # 替换所有路径非法字符为下划线
    
    # 生成唯一的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    case_path = os.path.join(case_dir, f"{safe_name}_{timestamp}.xlsx")
    
    # 确保父目录存在
    os.makedirs(os.path.dirname(case_path), exist_ok=True)
    
    if not os.path.exists(case_path):
        # 如果文件不存在，创建一个新文件
        with open(case_path, "w") as file:
            print(f"File '{case_path}' created.")
    else:
        print(f"File '{case_path}' already exists.")
    return case_path

def gen_story_core(story):
    """
    1. 基于需求 URL 生成回归用例
    """
    summary = chat_stream(extract_summary_prompt(story['description']))
    logger.info(f"摘要：\n {summary.choices[0].message.content}")
    cores = chat_stream(gen_function_modules_prompt(summary))
    cores = cores.choices[0].message.content
    # 去除可能存在的 ```json ```标记
    cores = re.sub(r'^```json\s*|\s*```$', '', cores, flags=re.MULTILINE)
    logger.info(f"核心功能：\n {cores}")
    cores = chat_stream(filter_relevant_modules_prompt(story['description'],cores))
    cores = cores.choices[0].message.content
    logger.info(f"check 后核心功能：\n {cores}")
    cores = re.sub(r'^```json\s*|\s*```$', '', cores, flags=re.MULTILINE)
    try:
        cores = json.loads(cores)
        return cores
    except (ValueError, SyntaxError) as e:
        logger.error(f"Failed to parse content: {e}")
        return None
    
from utils.modules import health_modules, health_add_modules, sass_modules
def find_modules(query,knowledge_base=""):
    """
    根据 query 查找最相关的模块目录
    :param query: 用户输入的查询字符串或字典
    :param coll: 知识库
    :return: 最相关的模块目录列表
    """
    if knowledge_base=="健康 checklist 用例":
        modules = health_modules
    elif knowledge_base=="健康增量用例":
        modules = health_add_modules
    elif knowledge_base=="SAAS用例":
        modules = sass_modules
    else:
        modules = []
    try:
        # 确保输入是字符串格式（如果是字典则转换为JSON字符串）
        if isinstance(query, dict):
            query_str = json.dumps(query, ensure_ascii=False)
        else:
            query_str = str(query)
        

        # 调用LLM获取响应
        res = chat_stream(find_modules_prompt(query_str,modules), max_tokens=2046)
        
        # 检查响应有效性
        if not res or not hasattr(res, 'choices') or len(res.choices) == 0:
            logger.error("获取模块目录失败：无效的LLM响应")
            return []
            
        content = res.choices[0].message.content
        
        # 清理响应内容（去除可能的代码标记）
        cleaned_content = re.sub(r'^```(json)?\s*|\s*```$', '', content, flags=re.MULTILINE).strip()
        
        # 尝试解析为Python对象
        try:
            modules = ast.literal_eval(cleaned_content)
        except (ValueError, SyntaxError) as e:
            logger.error(f"解析模块目录失败：{str(e)}")
            # 尝试作为纯字符串处理
            if cleaned_content.startswith('[') and cleaned_content.endswith(']'):
                modules = json.loads(cleaned_content)
            else:
                modules = [cleaned_content]
                
        # 确保返回的是列表格式
        if not isinstance(modules, list):
            modules = [modules] if modules else []
            
        return modules
        
    except json.JSONDecodeError as e:
        logger.error(f"JSON解析失败：{str(e)}")
        return []
    except Exception as e:
        logger.error(f"查找模块目录时发生意外错误：{str(e)}")
        return []

from rag_server.search import search_document,search,rerank
from utils.llm.prompt import check_query_relevant_module_prompt
def find_modules_for_saas(query):
    parent_module = find_modules(query,knowledge_base="SAAS用例")
    logger.info(f"查询到的父模块: {parent_module}")
    filter_expr = f'module in ("{parent_module[0]}")'
    logger.info(f"查询过滤条件: {filter_expr}")
    res = search(
        ns_code=os.getenv("SAAS_NAMESPACE"),
        coll_code=[os.getenv("SAAS_MODULE_ALL_COLL")],
        docs=[query],
        limit=20,
        filter_expr=filter_expr,
    )
    data = res.json()
    logger.info(f"调用trag返回的saas目录数据: {data}")
    docs = [
        doc["doc"]
        for docs_group in data["data"]["documents"].values()
        for doc in docs_group[0]
    ]
    logger.info(f"正在查询SAAS模块目录: {query}\n")
    logger.info(f"粗召回的文档数量: {docs}")
    rerank_data = rerank(
        rag_code=os.getenv("RAG_CODE"),
        namespace_code=os.getenv("SAAS_NAMESPACE"),
        documents=docs,
        query=query,
        model="bge-reranker-v2-m3"
    )
    rerank_data = rerank_data.json()
    # 取重排序后的前15个 index
    rerank_index = [item["index"] for item in rerank_data["data"][:15]]
    # 按 index 取出对应文档
    rerank_modules = [docs[i] for i in rerank_index]
    logger.info(f"重排序后的模块目录: {rerank_modules}")
    # LLM 筛选相关模块目录
    # res = chat_stream(check_query_relevant_module_prompt(query,rerank_modules), max_tokens=4096)
    # modules = res.choices[0].message.content
    # logger.info(f"筛选后的模块目录: {modules}\n")
    # modules = ast.literal_eval(modules)

    # if not isinstance(modules, list):
    #     modules = [modules] if modules else []
    # # 确保返回的是列表格式
    # if not modules:
    #     logger.warning(f"未找到与查询 '{query}' 相关的模块目录")
    #     return []
    # if not isinstance(modules, list):
    #     return []
    return rerank_modules



def extract_module_from_story(story):
    """
    从需求文档中提取模块
    :param story: 需求文档
    :return: 模块列表
    """
    prompt = gen_function_modules_prompt(story)
    modules = chat_stream(prompt, max_tokens=8096).choices[0].message.content
    logger.info(f"提取的功能列表: {modules}")
    # modules = chat_stream(polish_function_modules_prompt(story['description'],modules)).choices[0].message.content
    # logger.info(f"润色后的功能列表: {modules}")
    modules = chat_stream(filter_relevant_modules_prompt(story['description'],modules)).choices[0].message.content
    logger.info(f"筛选后的功能列表: {modules}")  
    modules = json.loads(modules)
    modules = modules[:8] if len(modules) > 8 else modules  # 不超过8个则保留全部
    logger.info(f"最终功能列表: {modules}")
    return modules

def summary_reasons(resons):
    """
     总结不召回用例的原因
    """

from utils.llm.prompt import polish_story_prompt
def polish_story(story):
    """
    对需求文档进行润色
    :param story: 需求文档
    :return: 润色后的需求文档
    """
    prompt = polish_story_prompt(story)
    polished_story = chat_stream(prompt, max_tokens=16192).choices[0].message.content
    logger.info(f"润色后的需求文档: {polished_story}")
    return polished_story
 
def gen_regress_case(history,story_url,knowledge_base):
    tapd_utils = TAPDUtils()
    story = tapd_utils.get_story(story_url)

    history.append({"role": "user", "content": f"帮我分析需求文档，生成回归用例集～"})
    
    # 生成用例文件
    result_path=gen_case_path(story['name'])
    yield history,result_path
    history.append({"role": "assistant", "content": f"## 正在分析需求: {story['name']}>>>\n"})
    logger.info(f"需求:{story['name']}\nstory_url:{story_url}")
    yield history,result_path
    
    # 解析需求核心功能模块
    # cores = gen_story_core(story)
    cores = extract_module_from_story(story)
    
    # 初始化Dify客户端
    dify_client = DifyClient()
    workspace_id, story_id = tapd_utils.match_url(story_url).groups()
    if workspace_id == os.getenv("HEALTH_WORKSPACE_ID"): 
        logger.info(f"正在检索健康知识库用例集")
        namespace = os.getenv("HEALTH_NAMESPACE")
        if knowledge_base=="健康 checklist 用例":
            collection_codes = os.getenv("HEALTH_MODULE_CHECKLIST_CASE_COLL")
        elif knowledge_base=="健康增量用例":
            collection_codes = os.getenv("HEALTH_MODULE_ADD_CASE_COLL")
    elif workspace_id == os.getenv("SAAS_WORKSPACE_ID"):
        if knowledge_base=="SAAS用例":
            logger.info(f"正在检索SAAS知识库用例集")
            namespace = os.getenv("SAAS_NAMESPACE")
            collection_codes = os.getenv("SAAS_MODULE_CASE_4_COLL")

    res_cases = []
    for item in cores:
        core ={}
        core['name']=item['name']
        core['description']=item['description']
        core['story']=story['name']
        core['模块']=story['category_name']
        history.append({"role": "assistant", "content": f"#### 正在召回模块用例\n 模块名称:{item['name']}\n模块描述: {item['description']}>>>"})
        yield history,result_path
        import time
        start_time = time.time()
        # 根据核心功能描述查询最相关的模块目录
        # modules = find_modules(core, knowledge_base)
        modules = find_modules_for_saas(json.dumps(core, ensure_ascii=False))
        logger.info(f"相关模块目录: {modules}\n")
        core['模块'] = modules
        query = []
        core = json.dumps(core, ensure_ascii=False)
        logger.info(f"当前检索 query: {core}")
        query.append(core)
        query = json.dumps(query, ensure_ascii=False)
        try:
            # 调用Dify API获取用例数据
            res = dify_client.query_dify(query, namespace, collection_codes)
            res = json.loads(res)
            cases = json.loads(res.get("result", "[]"))
            reason = json.loads(res.get("reason", "[]"))
            end_time = time.time()
            for case in cases:
                res_cases.append(case)
            # 打印当前模块结果
            res_str = json.dumps(cases, ensure_ascii=False, indent=4)
            history.append({"role": "assistant", "content": f"召回用例结果:\n{res_str}"})
            if reason:
                reason_str = json.dumps(reason, ensure_ascii=False, indent=4)
                history.append({"role": "assistant", "content": f"不召回原因:\n{reason_str}"})
            yield history, result_path
            logger.info(f"关于{query}的用例如下:\n {cases}\n 耗时: {end_time - start_time:.2f}秒")
        except Exception as e:
            logger.error(f"请求Dify API失败: {str(e)}")
            logger.error(f"失败的核心功能项: {query}")

    # 基于用例标题、步骤描述和预期结果三个字段去重
    unique_cases = []
    seen = set()
    for case in res_cases:
        if case is not None:
            key = (case.get("用例标题", ""), case.get("步骤描述", ""), case.get("预期结果", ""))
            key_str = json.dumps(key, ensure_ascii=False, sort_keys=True)
            if key_str not in seen:
                seen.add(key_str)
                unique_cases.append(case)
    to_excel(unique_cases, result_path)
    history.append({"role": "assistant", "content": f"## 回归用例召回完成，请查看文件～"})
    yield history,result_path

def to_excel(cases,path):
    # 处理数据格式
    processed_data = []
    for case in cases:
        processed_case = {
            "用例目录": case.get("用例目录", ""), 
            "用例标题": case.get("用例标题", ""),
            "步骤描述": case["步骤描述"].replace("1、", "").strip(),
            "预期结果": case["预期结果"].replace("1、", "").strip(),
            "用例等级": case["用例等级"]
    }
        processed_data.append(processed_case)

    # 创建DataFrame并指定列顺序
    columns_order = ["用例目录", "用例标题", "步骤描述", "预期结果", "用例等级"]
    df = DataFrame(processed_data, columns=columns_order)

    # 创建Excel writer对象
    writer = pd.ExcelWriter(path, engine='xlsxwriter')

    # 将DataFrame写入Excel
    df.to_excel(writer, sheet_name='回归用例', index=False)

    # 获取工作表对象
    workbook = writer.book
    worksheet = writer.sheets['回归用例']


    # 设置列宽格式
    format_wrap = workbook.add_format({'text_wrap': True})

    # 设置列宽（根据新字段顺序调整）
    worksheet.set_column('A:A', 30)    # 功能模块
    worksheet.set_column('B:B', 40)    # 用例标题
    worksheet.set_column('C:C', 50, format_wrap)  # 步骤描述
    worksheet.set_column('D:D', 60, format_wrap)  # 预期结果
    worksheet.set_column('E:E', 10)    # 用例等级

    # 保存Excel文件
    writer.close()
