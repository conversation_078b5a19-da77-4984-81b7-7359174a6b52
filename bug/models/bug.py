from sqlalchemy import Column, Integer, String, DateTime, Text, JSON, Boolean, Float, ForeignKey
from sqlalchemy.orm import relationship
from bug.database.database import Base
from bug.utils.time_util import get_china_now


class BugEvaluation(Base):
    """BUG评估记录表 - 存储完整的BUG数据和评估结果"""
    __tablename__ = "bug_evaluations"

    id = Column(Integer, primary_key=True, index=True)

    # BUG基本信息
    bug_id = Column(String(50), index=True)  # TAPD缺陷ID
    workspace_id = Column(String(50), index=True)  # 工作空间ID
    title = Column(String(500))  # 标题
    creator = Column(String(100))  # 创建人
    status = Column(String(50))  # 状态
    priority = Column(String(50))  # 优先级
    severity = Column(String(50))  # 严重程度
    description = Column(Text)  # 详细描述
    module = Column(String(200))  # 模块
    is_intercepted = Column(Boolean, default=False)  # 是否被拦截
    # 完整的BUG数据（JSON格式存储所有字段）
    bug_data = Column(JSON)

    # 评估结果汇总
    overall_passed = Column(Boolean, default=False)  # 整体是否通过
    common_missing_fields = Column(JSON)  # 缺失的普通字段

    # 用户反馈相关字段
    desc_fp = Column(Boolean, default=False)  # 详细描述是否误报
    desc_invalid = Column(Boolean, default=False)  # 建议是否有效
    title_fp = Column(Boolean, default=False)  # 标题是否误报
    title_invalid = Column(Boolean, default=False)  # 标题是否有效
    reason = Column(String(500))
    # 操作信息
    feedback_operator = Column(String(100))  # 反馈操作人员
    feedback_timestamp = Column(DateTime)  # 反馈操作时间
    feedback_action_value = Column(String(50))  # 反馈动作值（对应cards.py中的action_value）

    # 评估时间戳
    evaluated_at = Column(DateTime, default=get_china_now, index=True)
    created_at = Column(DateTime, default=get_china_now)
    updated_at = Column(DateTime, default=get_china_now, onupdate=get_china_now)

    # 关联评估详情
    title_evaluations = relationship("TitleEvaluation", back_populates="bug_evaluation", cascade="all, delete-orphan")
    description_evaluations = relationship("DescriptionEvaluation", back_populates="bug_evaluation",
                                           cascade="all, delete-orphan")
    suggest_evaluations = relationship("SuggestEvaluation", back_populates="bug_evaluation",
                                       cascade="all, delete-orphan")


class TitleEvaluation(Base):
    """标题评估结果表"""
    __tablename__ = "title_evaluations"

    id = Column(Integer, primary_key=True, index=True)
    bug_evaluation_id = Column(Integer, ForeignKey("bug_evaluations.id"), index=True)  # 关联主表

    field = Column(String(50))  # 字段名
    dimension_scores = Column(JSON)  # 维度评分
    passed = Column(Boolean)  # 是否通过
    feedback = Column(Text)  # 反馈信息
    suggest = Column(Text)  # 建议内容
    thinking_stage1 = Column(Text)  # 第一阶段思维过程
    thinking_stage2 = Column(Text)  # 第二阶段思维过程
    needed_elements = Column(JSON)  # 三要素必要性判断结果
    element_reason = Column(Text)  # 三要素判断原因

    created_at = Column(DateTime, default=get_china_now)

    # 关联主表
    bug_evaluation = relationship("BugEvaluation", back_populates="title_evaluations")


class DescriptionEvaluation(Base):
    """详细描述评估结果表"""
    __tablename__ = "description_evaluations"

    id = Column(Integer, primary_key=True, index=True)
    bug_evaluation_id = Column(Integer, ForeignKey("bug_evaluations.id"), index=True)  # 关联主表

    field = Column(String(50))  # 字段名
    dimension_scores = Column(JSON)  # 维度评分
    score = Column(Float, default=0.0)  # 总分
    passed = Column(Boolean)  # 是否通过
    feedback = Column(Text)  # 反馈信息
    suggest = Column(Text)  # 建议内容

    created_at = Column(DateTime, default=get_china_now)

    # 关联主表
    bug_evaluation = relationship("BugEvaluation", back_populates="description_evaluations")


class SuggestEvaluation(Base):
    """智能建议评估结果表"""
    __tablename__ = "suggest_evaluations"

    id = Column(Integer, primary_key=True, index=True)
    bug_evaluation_id = Column(Integer, ForeignKey("bug_evaluations.id"), index=True)  # 关联主表

    field = Column(String(50))  # 字段名（如：优先级、严重程度）
    suggested = Column(String(100))  # 建议值
    actual = Column(String(100))  # 实际值
    reason = Column(Text)  # 建议原因
    feedback = Column(Text)  # 反馈信息

    created_at = Column(DateTime, default=get_china_now)

    # 关联主表
    bug_evaluation = relationship("BugEvaluation", back_populates="suggest_evaluations")
