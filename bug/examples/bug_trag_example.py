"""
BUG TRAG 管理器使用示例
"""
import os
import sys
from dotenv import load_dotenv

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from bug.trag.bug_trag_manager import BugTRAGManager, create_bug_collection
from bug.utils.logger_util import logger
from bug.config.config import TRAG_TOKEN, TRAG_NAMESPACE
from trag import TRAG

load_dotenv()


def create_bug_knowledge_base():
    """创建BUG知识库"""
    try:
        # 初始化TRAG
        rag = TRAG.from_api_key(api_key=TRAG_TOKEN)
        ns = rag.namespace(TRAG_NAMESPACE)
        
        # 创建BUG知识库
        coll = create_bug_collection(
            ns=ns,
            name="bug_evaluations",
            description="BUG规范性评估数据知识库",
            embedding_model="public-bge-m3",
            dimension=1024
        )
        
        print(f"BUG知识库创建成功: {coll.info}")
        print(f"知识库代码: {coll.info.get('code', 'N/A')}")
        
        return coll
        
    except Exception as e:
        logger.error(f"创建BUG知识库失败: {str(e)}")
        return None


def import_bugs_example():
    """导入BUG数据示例"""
    try:
        # 初始化BUG TRAG管理器
        # 注意：需要先创建知识库并获取coll_code
        manager = BugTRAGManager(
            ns_code=TRAG_NAMESPACE,
            coll_code="your_bug_collection_code"  # 替换为实际的知识库代码
        )
        
        # 导入所有BUG数据
        count = manager.import_bugs_from_database()
        print(f"成功导入 {count} 个BUG到TRAG知识库")
        
        # 导入指定工作空间的BUG数据
        workspace_id = "your_workspace_id"  # 替换为实际的工作空间ID
        count = manager.import_bugs_from_database(workspace_id=workspace_id, limit=100)
        print(f"成功导入工作空间 {workspace_id} 的 {count} 个BUG到TRAG知识库")
        
    except Exception as e:
        logger.error(f"导入BUG数据失败: {str(e)}")


def search_bugs_example():
    """搜索BUG数据示例"""
    try:
        # 初始化BUG TRAG管理器
        manager = BugTRAGManager(
            ns_code=TRAG_NAMESPACE,
            coll_code="your_bug_collection_code"  # 替换为实际的知识库代码
        )
        
        # 基本搜索
        query = "登录功能异常"
        results = manager.search_bugs(query=query, limit=5)
        print(f"搜索 '{query}' 返回 {len(results)} 个结果:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result}")
        
        # 带过滤条件的搜索
        filter_expr = 'module == "用户管理" && priority == "高"'
        results = manager.search_bugs(
            query="用户登录问题", 
            limit=10, 
            filter_expr=filter_expr
        )
        print(f"过滤搜索返回 {len(results)} 个结果")
        
        # 按工作空间过滤
        filter_expr = 'workspace_id == "20375472"'
        results = manager.search_bugs(
            query="界面显示问题", 
            limit=10, 
            filter_expr=filter_expr
        )
        print(f"工作空间过滤搜索返回 {len(results)} 个结果")
        
        # 按评估结果过滤
        filter_expr = 'overall_passed == "False"'
        results = manager.search_bugs(
            query="BUG描述不规范", 
            limit=10, 
            filter_expr=filter_expr
        )
        print(f"未通过评估的BUG搜索返回 {len(results)} 个结果")
        
    except Exception as e:
        logger.error(f"搜索BUG数据失败: {str(e)}")


def clean_bugs_example():
    """清空BUG知识库示例"""
    try:
        # 初始化BUG TRAG管理器
        manager = BugTRAGManager(
            ns_code=TRAG_NAMESPACE,
            coll_code="your_bug_collection_code"  # 替换为实际的知识库代码
        )
        
        # 清空知识库
        manager.clean_bug_documents()
        print("BUG知识库清空完成")
        
    except Exception as e:
        logger.error(f"清空BUG知识库失败: {str(e)}")


def get_collection_info_example():
    """获取知识库信息示例"""
    try:
        # 初始化BUG TRAG管理器
        manager = BugTRAGManager(
            ns_code=TRAG_NAMESPACE,
            coll_code="your_bug_collection_code"  # 替换为实际的知识库代码
        )
        
        # 获取知识库信息
        info = manager.get_collection_info()
        print(f"BUG知识库信息: {info}")
        
    except Exception as e:
        logger.error(f"获取知识库信息失败: {str(e)}")


if __name__ == "__main__":
    print("BUG TRAG 管理器使用示例")
    print("=" * 50)
    
    # 1. 创建BUG知识库（首次使用时需要）
    print("1. 创建BUG知识库")
    create_bug_knowledge_base()
    
    # 2. 导入BUG数据
    print("\n2. 导入BUG数据")
    # import_bugs_example()  # 取消注释以执行
    
    # 3. 搜索BUG数据
    print("\n3. 搜索BUG数据")
    # search_bugs_example()  # 取消注释以执行
    
    # 4. 获取知识库信息
    print("\n4. 获取知识库信息")
    # get_collection_info_example()  # 取消注释以执行
    
    # 5. 清空知识库（谨慎使用）
    print("\n5. 清空知识库")
    # clean_bugs_example()  # 取消注释以执行
    
    print("\n示例完成！")
    print("注意：请根据实际情况修改配置参数后再运行相关功能。")
