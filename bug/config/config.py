from dotenv import load_dotenv
import os

print(os.path.dirname(__file__))
load_dotenv(os.path.join(os.path.dirname(__file__), '../.env'))

# Rag
TRAG_CASE_COLL = os.getenv('TRAG_CASE_COLL', "")
TRAG_WORKSPACE_ID = os.getenv('TRAG_WORKSPACE_ID', "")
TRAG_NAMESPACE = os.getenv('TRAG_NAMESPACE', "")

# BUG数据库配置
DB_USER = os.getenv('DB_USER', "")
DB_PASSWORD = os.getenv('DB_PASSWORD', "")
DB_HOST = os.getenv('DB_HOST', "")
DB_PORT = os.getenv('DB_PORT', "")
DB_NAME = os.getenv('DB_NAME', "")

# TRAG配置
TRAG_TOKEN = os.getenv('TRAG_TOKEN', "")
TRAG_BUG_COLL = os.getenv('TRAG_BUG_COLL', "")  # BUG专用知识库