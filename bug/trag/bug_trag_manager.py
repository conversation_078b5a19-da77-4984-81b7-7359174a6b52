import os
import json
from typing import List, Optional
from dotenv import load_dotenv
from trag import TRAG
from trag.types.document import Document
from trag.types.collection import CollectionFieldInfo

from bug.database.database import init_db, get_all_bug_evaluations
from bug.models.bug import BugEvaluation, TitleEvaluation, DescriptionEvaluation, SuggestEvaluation
from bug.utils.logger_util import logger
from bug.config.config import TRAG_TOKEN, TRAG_NAMESPACE, TRAG_BUG_COLL

load_dotenv()


class BugTRAGManager:
    """
    BUG TRAG 管理类，负责将BUG规范性数据库中的数据加载到TRAG知识库中
    """

    def __init__(self, ns_code=None, coll_code=None):
        """
        初始化 BUG TRAG 管理器
        :param ns_code: 命名空间代码
        :param coll_code: 知识库集合代码
        """
        # 初始化数据库连接
        init_db()
        
        # 初始化TRAG连接
        self.rag = TRAG.from_api_key(api_key=TRAG_TOKEN)
        self.namespace = self.rag.namespace(ns_code or TRAG_NAMESPACE)
        self.collection = self.namespace.collection(coll_code or TRAG_BUG_COLL)
        
        logger.info(f"BugTRAGManager 初始化完成: {self.rag.info}")

    def _generate_bug_document(self, bug_evaluation: BugEvaluation) -> Document:
        """
        生成BUG文档对象
        :param bug_evaluation: BUG评估数据
        :return: Document 对象
        """
        # 构建用于嵌入的简化BUG信息
        embedding_content = self._build_embedding_content(bug_evaluation)
        
        # 构建完整的BUG数据（用于存储）
        full_bug_data = self._build_full_bug_data(bug_evaluation)
        
        # 构建文档键值对（用于过滤）
        doc_key_value = {
            "workspace_id": bug_evaluation.workspace_id,
            "module": bug_evaluation.module or "未分类",
            "status": bug_evaluation.status or "未知",
            "priority": bug_evaluation.priority or "未知",
            "severity": bug_evaluation.severity or "未知",
            "overall_passed": str(bug_evaluation.overall_passed),
            "creator": bug_evaluation.creator or "未知"
        }
        
        return Document(
            id=f"bug_{bug_evaluation.bug_id}",
            embedding_query=embedding_content,
            doc=json.dumps(full_bug_data, ensure_ascii=False, indent=2),
            doc_key_value=doc_key_value
        )

    def _build_embedding_content(self, bug_evaluation: BugEvaluation) -> str:
        """
        构建用于嵌入的BUG内容
        :param bug_evaluation: BUG评估数据
        :return: 用于嵌入的字符串
        """
        content_parts = [
            f"BUG ID: {bug_evaluation.bug_id}",
            f"标题: {bug_evaluation.title or ''}",
            f"模块: {bug_evaluation.module or ''}",
            f"状态: {bug_evaluation.status or ''}",
            f"优先级: {bug_evaluation.priority or ''}",
            f"严重程度: {bug_evaluation.severity or ''}",
            f"创建人: {bug_evaluation.creator or ''}",
            f"描述: {bug_evaluation.description or ''}",
        ]
        
        # 添加评估结果信息
        if bug_evaluation.title_evaluations:
            for title_eval in bug_evaluation.title_evaluations:
                if title_eval.feedback:
                    content_parts.append(f"标题评估反馈: {title_eval.feedback}")
                if title_eval.suggest:
                    content_parts.append(f"标题建议: {title_eval.suggest}")
        
        if bug_evaluation.description_evaluations:
            for desc_eval in bug_evaluation.description_evaluations:
                if desc_eval.feedback:
                    content_parts.append(f"描述评估反馈: {desc_eval.feedback}")
                if desc_eval.suggest:
                    content_parts.append(f"描述建议: {desc_eval.suggest}")
        
        if bug_evaluation.suggest_evaluations:
            for suggest_eval in bug_evaluation.suggest_evaluations:
                if suggest_eval.reason:
                    content_parts.append(f"智能建议({suggest_eval.field}): {suggest_eval.reason}")
        
        return "\n".join(content_parts)

    def _build_full_bug_data(self, bug_evaluation: BugEvaluation) -> dict:
        """
        构建完整的BUG数据
        :param bug_evaluation: BUG评估数据
        :return: 完整的BUG数据字典
        """
        bug_data = {
            "basic_info": {
                "bug_id": bug_evaluation.bug_id,
                "workspace_id": bug_evaluation.workspace_id,
                "title": bug_evaluation.title,
                "creator": bug_evaluation.creator,
                "status": bug_evaluation.status,
                "priority": bug_evaluation.priority,
                "severity": bug_evaluation.severity,
                "description": bug_evaluation.description,
                "module": bug_evaluation.module,
                "is_intercepted": bug_evaluation.is_intercepted,
                "overall_passed": bug_evaluation.overall_passed,
                "common_missing_fields": bug_evaluation.common_missing_fields,
                "evaluated_at": bug_evaluation.evaluated_at.isoformat() if bug_evaluation.evaluated_at else None,
                "created_at": bug_evaluation.created_at.isoformat() if bug_evaluation.created_at else None,
            },
            "original_bug_data": bug_evaluation.bug_data,
            "feedback_info": {
                "desc_fp": bug_evaluation.desc_fp,
                "desc_invalid": bug_evaluation.desc_invalid,
                "title_fp": bug_evaluation.title_fp,
                "title_invalid": bug_evaluation.title_invalid,
                "reason": bug_evaluation.reason,
                "feedback_operator": bug_evaluation.feedback_operator,
                "feedback_timestamp": bug_evaluation.feedback_timestamp.isoformat() if bug_evaluation.feedback_timestamp else None,
                "feedback_action_value": bug_evaluation.feedback_action_value,
            },
            "evaluations": {
                "title_evaluations": [],
                "description_evaluations": [],
                "suggest_evaluations": []
            }
        }
        
        # 添加标题评估详情
        for title_eval in bug_evaluation.title_evaluations:
            bug_data["evaluations"]["title_evaluations"].append({
                "field": title_eval.field,
                "dimension_scores": title_eval.dimension_scores,
                "passed": title_eval.passed,
                "feedback": title_eval.feedback,
                "suggest": title_eval.suggest,
                "thinking_stage1": title_eval.thinking_stage1,
                "thinking_stage2": title_eval.thinking_stage2,
                "needed_elements": title_eval.needed_elements,
                "element_reason": title_eval.element_reason,
                "created_at": title_eval.created_at.isoformat() if title_eval.created_at else None,
            })
        
        # 添加描述评估详情
        for desc_eval in bug_evaluation.description_evaluations:
            bug_data["evaluations"]["description_evaluations"].append({
                "field": desc_eval.field,
                "dimension_scores": desc_eval.dimension_scores,
                "score": desc_eval.score,
                "passed": desc_eval.passed,
                "feedback": desc_eval.feedback,
                "suggest": desc_eval.suggest,
                "created_at": desc_eval.created_at.isoformat() if desc_eval.created_at else None,
            })
        
        # 添加智能建议评估详情
        for suggest_eval in bug_evaluation.suggest_evaluations:
            bug_data["evaluations"]["suggest_evaluations"].append({
                "field": suggest_eval.field,
                "suggested": suggest_eval.suggested,
                "actual": suggest_eval.actual,
                "reason": suggest_eval.reason,
                "feedback": suggest_eval.feedback,
                "created_at": suggest_eval.created_at.isoformat() if suggest_eval.created_at else None,
            })
        
        return bug_data

    def import_bugs_from_database(self, workspace_id: Optional[str] = None, limit: Optional[int] = None) -> int:
        """
        从数据库导入BUG数据到TRAG
        :param workspace_id: 工作空间ID，如果指定则只导入该工作空间的BUG
        :param limit: 限制导入数量
        :return: 成功导入的BUG数量
        """
        try:
            # 获取BUG评估数据
            bug_evaluations = get_all_bug_evaluations(workspace_id)
            
            if limit:
                bug_evaluations = bug_evaluations[:limit]
            
            if not bug_evaluations:
                logger.warning("没有找到BUG评估数据")
                return 0
            
            # 生成文档列表
            documents = []
            for bug_eval in bug_evaluations:
                try:
                    document = self._generate_bug_document(bug_eval)
                    documents.append(document)
                    logger.debug(f"生成BUG文档: {bug_eval.bug_id}")
                except Exception as e:
                    logger.error(f"生成BUG文档失败 {bug_eval.bug_id}: {str(e)}")
                    continue
            
            if not documents:
                logger.warning("没有生成任何有效的BUG文档")
                return 0
            
            # 导入到TRAG
            self.collection.import_documents(documents)
            logger.info(f"成功导入 {len(documents)} 个BUG到TRAG知识库")
            
            return len(documents)
            
        except Exception as e:
            logger.error(f"导入BUG数据到TRAG失败: {str(e)}")
            raise

    def search_bugs(self, query: str, limit: int = 10, filter_expr: str = '') -> List:
        """
        搜索BUG数据
        :param query: 搜索查询字符串
        :param limit: 返回结果数量限制
        :param filter_expr: 过滤表达式
        :return: 搜索结果列表
        """
        try:
            results = self.collection.search_documents(doc=query, limit=limit, filter_expr=filter_expr)
            logger.info(f"BUG搜索完成，返回 {len(results)} 个结果")
            return results
        except Exception as e:
            logger.error(f"搜索BUG数据失败: {str(e)}")
            return []

    def clean_bug_documents(self):
        """清空BUG知识库中的所有文档"""
        try:
            self.collection.clean_documents()
            logger.info("BUG知识库清空完成")
        except Exception as e:
            logger.error(f"清空BUG知识库失败: {str(e)}")
            raise

    def get_collection_info(self):
        """获取知识库信息"""
        return self.collection.info


def create_bug_collection(ns, name="bug_evaluations", description="BUG规范性评估数据知识库", 
                         embedding_model="public-bge-m3", dimension=1024):
    """
    创建BUG知识库集合
    :param ns: 命名空间对象
    :param name: 知识库名称
    :param description: 知识库描述
    :param embedding_model: 嵌入模型名称
    :param dimension: 嵌入维度
    :return: 创建的知识库集合对象
    """
    # 定义字段信息
    field_infos = [
        CollectionFieldInfo(fieldName="workspace_id", fieldType="string"),
        CollectionFieldInfo(fieldName="module", fieldType="string"),
        CollectionFieldInfo(fieldName="status", fieldType="string"),
        CollectionFieldInfo(fieldName="priority", fieldType="string"),
        CollectionFieldInfo(fieldName="severity", fieldType="string"),
        CollectionFieldInfo(fieldName="overall_passed", fieldType="string"),
        CollectionFieldInfo(fieldName="creator", fieldType="string"),
    ]
    
    col = ns.create_collection(
        name=name, 
        description=description, 
        embedding_model=embedding_model,
        dimension=dimension, 
        field_list=field_infos
    )
    
    logger.info(f"BUG知识库创建完成: {col.info}")
    return col
