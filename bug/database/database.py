from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from bug.utils.logger_util import logger
from sqlalchemy import and_
from sqlalchemy.orm import Session

# 创建基类
Base = declarative_base()

# 全局变量初始化为 None
_engine = None
SessionLocal = None
        

def init_db():
    """延迟初始化数据库连接，在需要时显式调用"""
    global _engine, SessionLocal

    # 从环境变量获取数据库配置
    DB_USER = os.getenv('DB_USER')
    DB_PASSWORD = os.getenv('DB_PASSWORD')
    DB_HOST = os.getenv('DB_HOST')
    DB_PORT = os.getenv('DB_PORT')
    DB_NAME = os.getenv('DB_NAME')

    if not all([DB_USER, DB_PASSWORD, DB_HOST, DB_PORT, DB_NAME]):
        raise ValueError("环境变量未完全设置，无法初始化数据库连接。")

    # 构建数据库URL
    SQLALCHEMY_DATABASE_URL = f"mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

    # 创建数据库引擎和会话工厂
    _engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_pre_ping=True,
        pool_recycle=3600,
    )
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=_engine)

def get_engine():
    if _engine is None:
        raise RuntimeError("Engine 尚未初始化，请先调用 init_db()。")
    return _engine


# 获取数据库会话的依赖函数
def get_db():
    if SessionLocal is None:
        raise RuntimeError("数据库未初始化，请先调用 init_db()。")
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def create_tables():
    """创建所有表"""
    from bug.models.bug import BugEvaluation, TitleEvaluation, DescriptionEvaluation, SuggestEvaluation
    
    if _engine is None:
        init_db()
    
    engine = get_engine()
    Base.metadata.create_all(bind=engine)
    logger.info("数据库表创建完成")


def get_bug_from_dataset(workspace_id: str) -> list:
    """从数据集中获取BUG数据"""
    try:
        from bug.models.bug import BugEvaluation
        
        db_gen = get_db()
        db: Session = next(db_gen)
        bugs = db.query(BugEvaluation.bug_id).filter(BugEvaluation.workspace_id == workspace_id).all()
        db.close()
        bug_id_list = [bug.bug_id for bug in bugs]
        
        # 这里需要根据实际情况调用TAPD客户端获取完整数据
        # pure_bug_data_list = []
        # for bug_id in bug_id_list:
        #     pure_bug_data = tap_client.get_bug_all_pure_message(workspace_id, bug_id)
        #     pure_bug_data_list.append(pure_bug_data)
        # return pure_bug_data_list
        
        return bug_id_list
    except Exception as e:
        logger.error(f"获取数据集失败: {str(e)}")
        return []


def get_all_bug_evaluations(workspace_id: str = None) -> list:
    """获取所有BUG评估数据"""
    try:
        from bug.models.bug import BugEvaluation
        
        db_gen = get_db()
        db: Session = next(db_gen)
        
        query = db.query(BugEvaluation)
        if workspace_id:
            query = query.filter(BugEvaluation.workspace_id == workspace_id)
            
        bugs = query.all()
        db.close()
        return bugs
    except Exception as e:
        logger.error(f"获取BUG评估数据失败: {str(e)}")
        return []
