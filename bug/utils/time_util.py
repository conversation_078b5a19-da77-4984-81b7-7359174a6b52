from datetime import datetime
import pytz


def get_china_now():
    """获取中国时区的当前时间"""
    china_tz = pytz.timezone('Asia/Shanghai')
    return datetime.now(china_tz)


def format_datetime(dt, format_str='%Y-%m-%d %H:%M:%S'):
    """格式化日期时间"""
    if dt is None:
        return None
    return dt.strftime(format_str)


def parse_datetime(dt_str, format_str='%Y-%m-%d %H:%M:%S'):
    """解析日期时间字符串"""
    if not dt_str:
        return None
    return datetime.strptime(dt_str, format_str)
