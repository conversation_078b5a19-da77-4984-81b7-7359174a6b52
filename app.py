import gradio as gr
from tools.review import ai_review
from tools.gen_usecase import ai_case
from tools.regress import gen_regress_case


with gr.Blocks(title="AI用例系统", theme=gr.themes.Soft(font=[gr.themes.GoogleFont("Inconsolata"), "Arial", "sans-serif"])) as demo:
    gr.<PERSON><PERSON>("# 🧠 AI用例系统")
    
    with gr.<PERSON>bs():
        with gr.TabItem("RAG回归用例"):
            gr.Markdown("上传需求URL，基于知识库获取回归用例（当前支持健康业务和 Sass 业务）")
            with gr.Row():
                with gr.Column(scale=1):
                    regress_url = gr.Textbox(
                        label="TAPD需求链接",
                        placeholder="请输入完整的TAPD需求URL...",
                        lines=2
                    )
                    knowledge_base = gr.Radio(
                        label="知识库",
                        choices=["SAAS用例","健康 checklist 用例", "健康增量用例"],
                        value="SAAS用例"
                    )
                    regress_btn = gr.<PERSON><PERSON>("开始生成", variant="primary")
                    # 示例输入
                    gr.Examples(
                        examples=[
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919229"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122722991"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472121659512"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919425"],
                        ],
                        inputs=[regress_url],
                    )
                    
                with gr.Column(scale=2):
                    regress_chatbot = gr.Chatbot(
                        label="回归用例生成",
                        avatar_images=("resources/img/user.jpeg", "resources/img/bot.jpeg"),
                        height=700,
                        show_share_button=True,
                        type="messages",
                    )
                    regress_result = gr.File(
                        label="回归用例",
                        height=50
                    )
            
            regress_btn.click(
                fn=gen_regress_case,
                inputs=[regress_chatbot, regress_url, knowledge_base],
                outputs=[regress_chatbot, regress_result]
            )

        with gr.TabItem("用例评审"):
            gr.Markdown("上传用例文件并输入需求URL，获取AI评审意见")
            with gr.Row():
                with gr.Column(scale=1):
                    usecase_file = gr.File(
                        label="上传用例文件",
                        file_types=[".xmind", ".xlsx", ".xls"],
                        type="filepath",
                        height=150
                    )
                    review_url = gr.Textbox(
                        label="TAPD需求链接",
                        placeholder="请输入完整的TAPD需求URL...",
                        lines=2
                    )
                    review_btn = gr.Button("开始评审", variant="primary")
                    # 示例输入
                    gr.Examples(
                        examples=[
                            ["resources/demo/【产品需求】AIGC「健康问问」接入Hunyuan-T1引擎支持深度思考.xmind", "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472121991502"],
                            ["resources/demo/【产品需求】AIGC「健康问问」联动健康管理服务V1.0.xmind", "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472121535000"]
                        ],
                        inputs=[usecase_file, review_url],
                    )
                    
                with gr.Column(scale=2):
                    review_chatbot = gr.Chatbot(
                        label="评审对话",
                        avatar_images=("resources/img/user.jpeg", "resources/img/bot.jpeg"),
                        height=700,
                        show_share_button=True,
                        type="messages",
                    )
                    review_result = gr.File(
                        label="评审结果",
                        height=50
                    )
            
            review_btn.click(
                fn=ai_review,
                inputs=[review_chatbot, usecase_file, review_url],
                outputs=[review_chatbot, review_result]
            )

        with gr.TabItem("用例生成"):
            gr.Markdown("上传需求URL，获取推荐用例")
            with gr.Row():
                with gr.Column(scale=1):
                    generate_url = gr.Textbox(
                        label="TAPD需求链接",
                        placeholder="请输入完整的TAPD需求URL...",
                        lines=2
                    )
                    generate_btn = gr.Button("开始生成", variant="primary")
                    # 示例输入
                    gr.Examples(
                        examples=[
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122143306"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122722991"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472121659512"],
                            ["https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472119919425"],
                            []
                        ],
                        inputs=[generate_url],
                    )
                    
                with gr.Column(scale=2):
                    generate_chatbot = gr.Chatbot(
                        label="生成用例",
                        avatar_images=("resources/img/user.jpeg", "resources/img/bot.jpeg"),
                        height=700,
                        show_share_button=True,
                        type="messages",
                    )
                    generate_result = gr.File(
                        label="生成用例",
                        height=50
                    )
            
            generate_btn.click(
                fn=ai_case,
                inputs=[generate_chatbot, generate_url],
                outputs=[generate_chatbot, generate_result]
            )


if __name__ == "__main__":
    demo.queue(default_concurrency_limit=5).launch(
        server_name="0.0.0.0",
        server_port=8006,
        show_api=False
    )
