import os
import re
import openpyxl
import json
from dotenv import load_dotenv
from trag import TRAG
from trag.types.document import Document
from utils.tapd import HTMLProcessor, TAPDClient, AppConfig
from trag.types.collection import CollectionFieldInfo

# 导入BUG TRAG管理器
try:
    import sys
    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
    from bug.trag.bug_trag_manager import BugTRAGManager, create_bug_collection
    from bug.utils.logger_util import logger as bug_logger
    BUG_TRAG_AVAILABLE = True
except ImportError as e:
    print(f"BUG TRAG模块导入失败: {e}")
    BUG_TRAG_AVAILABLE = False

load_dotenv()


class TAPDUtils:
    """
    TAPD 相关操作工具类，负责处理 TAPD URL 解析和数据获取。
    """

    def __init__(self):
        """初始化 TAPD 工具类，加载配置和客户端。"""
        self.config = AppConfig()
        self.client = TAPDClient(auth_token=self.config.auth_token)
        self.processor = HTMLProcessor(
            excluded_tags=self.config.excluded_tags,
            markdown_config=self.config.markdown_config
        )

    def get_story_name(self, url):
        """
        从 TAPD URL 中获取需求名称。
        :param url: TAPD 需求 URL
        :return: 需求名称（字符串），如果 URL 无效则返回 None
        """
        match = self._match_url(url)
        if match:
            workspace_id, story_id = match.groups()
            return self.client.get_story_name(workspace_id, story_id)
        return None

    def get_story_description(self, url):
        """
        从 TAPD URL 中获取需求描述（Markdown 格式）。
        :param url: TAPD 需求 URL
        :return: 需求描述（Markdown 格式），如果 URL 无效则返回 None
        """
        match = self._match_url(url)
        if match:
            workspace_id, story_id = match.groups()
            description = self.client.get_story_description(workspace_id, story_id)
            return self.processor.convert_to_markdown(description)
        return None

    def get_story(self, url):
        """
        从 TAPD URL 中获取完整需求信息。
        :param url: TAPD 需求 URL
        :return: 需求字典（包含描述等字段），如果 URL 无效则返回 None
        """
        match = self._match_url(url)
        if match:
            workspace_id, story_id = match.groups()
            story = self.client.get_story(workspace_id, story_id)
            if story:
                story['description'] = self.processor.convert_to_markdown(story.get('description', ''))
            return story
        return None

    def _match_url(self, url):
        """
        匹配 TAPD URL 并提取 workspace_id 和 story_id。
        :param url: TAPD 需求 URL
        :return: 匹配结果（re.Match 对象），如果 URL 无效则返回 None
        """
        patterns = [
            r"/tapd_fe/(\d+)/story/detail/(\d+)",
            r'https://tapd\.woa\.com/(\d+)/prong/stories/view/(\d+)',
            r"/tapd_fe/(\d+)/.*?dialog_preview_id=story_(\d+)"
        ]
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match
        return None


class TRAGManager:
    """
    TRAG 文档管理类，负责文档的导入、搜索和清理。
    """

    def __init__(self, ns_code=os.getenv("SAAS_NAMESPACE"), coll_code=os.getenv("SAAS_CASE_COLL")):
        """
        初始化 TRAG 连接和 TAPD 工具。
        :param ns_code: 命名空间代码（从环境变量获取）
        :param coll_code: 知识库集合代码（从环境变量获取）
        """
        self.rag = TRAG.from_api_key(api_key=os.getenv("TRAG_TOKEN"))
        self.namespace = self.rag.namespace(ns_code)
        self.collection = self.namespace.collection(coll_code)
        self.tapd_utils = TAPDUtils()
        print(self.rag.info)

    def _parse_excel_row(self, row, headers):
        """
        解析 Excel 单行数据。
        :param row: Excel 行数据
        :param headers: 表头列表
        :return: 解析后的字典
        """
        return {header: row[i] if row[i] is not None else "" for i, header in enumerate(headers)}

    def _generate_case_document(self, row_data):
        """
        生成测试用例文档对象。
        :param row_data: 用例数据字典
        :return: Document 对象
        """
        simple_case = (
            f"用例目录: {row_data['用例目录']}\\n"
            f"用例标题: {row_data['用例标题']}\\n"
            f"步骤描述: {row_data['步骤描述']}\\n"
            f"预期结果: {row_data['预期结果']}\\n"
        )
        # 获取目录作为模块标签（如果存在）
        parts = row_data['用例目录'].split('/')
        module = '/'.join(parts[1:])
        original_case = json.dumps(row_data, ensure_ascii=False, indent=2)
        return Document(
            id=row_data['用例ID'],
            embedding_query=simple_case,
            doc=original_case,
            doc_key_value={"module": module}
        )

    def import_test_cases(self, file_path):
        """
        从 Excel 导入测试用例到 TRAG。
        :param file_path: Excel 文件路径
        """
        headers = [
            "用例ID", "UUID", "用例标题", "用例目录", "用例描述", "用例负责人",
            "用例类型", "是否自动化", "等级", "前置条件", "步骤描述类型",
            "步骤描述", "预期结果", "评估工时", "标签", "关联需求",
            "关联的自动化用例", "执行次数", "创建人", "创建时间"
        ]
        workbook = openpyxl.load_workbook(file_path)
        sheet = workbook.active
        documents = []
        for row in sheet.iter_rows(min_row=2, values_only=True):
            row_data = self._parse_excel_row(row, headers)
            document = self._generate_case_document(row_data)
            documents.append(document)
        workbook.close()

        self.collection.import_documents(documents)
        print(f"成功导入 {len(documents)} 个测试用例")

    def import_require(self, urls):
        """
        从 TAPD URL 列表导入需求文档到 TRAG。
        :param urls: TAPD 需求 URL 列表
        """
        documents = []
        for url in urls:
            require = self.tapd_utils.get_story(url)
            if require:
                document = self._generate_require_document(require)
                documents.append(document)
                print(document.embedding_query)  # 打印简化版需求信息
        self.collection.import_documents(documents)
        print(f"成功导入 {len(documents)} 个需求")

    def search_test_cases(self, query, limit=10, filter_expr=''):
        """
        搜索测试用例。
        :param query: 搜索查询字符串
        :param limit: 返回结果数量限制
        :param filter_expr: 过滤表达式（可选）
        :return: 搜索结果列表
        """
        return self.collection.search_documents(doc=query, limit=limit, filter_expr=filter_expr)

    def clean_documents(self):
        """清空 TRAG 文档库。"""
        self.collection.clean_documents()

    def _generate_require_document(self, require):
        """
        生成需求文档对象。
        :param require: 需求数据字典
        :return: Document 对象
        """
        require_str = json.dumps(require, ensure_ascii=False, indent=2)
        return Document(
            id=require['id'],
            embedding_query=require_str,
            doc=require_str
        )

    def import_case_directories(self, file_path):
        """
        从 Excel 文件导入用例目录到知识库。
        :param file_path: Excel 文件路径
        """
        headers = [
            "用例ID", "UUID", "用例标题", "用例目录", "用例描述", "用例负责人",
            "用例类型", "是否自动化", "等级", "前置条件", "步骤描述类型",
            "步骤描述", "预期结果", "评估工时", "标签", "关联需求",
            "关联的自动化用例", "执行次数", "创建人", "创建时间"
        ]
        workbook = openpyxl.load_workbook(file_path)
        sheet = workbook.active
        documents = []
        processed_directories = set()
        for row in sheet.iter_rows(min_row=2, values_only=True):
            row_data = {header: row[i] if row[i] is not None else "" for i, header in enumerate(headers)}
            directory = row_data['用例目录']
            parts = directory.split('/')

            # 提取第二层目录作为 module（如果存在）
            directory = '/'.join(parts[1:])
            module = parts[1] if len(parts) > 1 else ""

            if directory not in processed_directories:
                document = Document(
                    id=row_data['用例ID'],
                    embedding_query=directory,
                    doc=directory,
                    doc_key_value={"module": module}
                )
                documents.append(document)
                processed_directories.add(directory)
        workbook.close()
        self.collection.import_documents(documents)
        print(f"成功导入 {len(documents)} 个用例目录")


def create_ns(name):
    """
    创建命名空间。
    :param name: 命名空间名称
    :return: 创建的命名空间对象
    """
    rag = TRAG.from_api_key(api_key=os.getenv("TRAG_TOKEN"))
    return rag.create_namespace(name, description="")


def create_coll(ns, name, description="test", embedding_model="public-bge-m3", dimension=1024):
    """
    创建知识库集合。
    :param ns: 命名空间对象
    :param name: 知识库名称
    :param description: 知识库描述（默认："test"）
    :param embedding_model: 嵌入模型名称（默认："public-bge-m3"）
    :param dimension: 嵌入维度（默认：1024）
    :return: 创建的知识库集合对象
    """
    return ns.create_collection(name=name, description=description, embedding_model=embedding_model,
                                dimension=dimension)


def create_field_coll(ns, name, description="test", embedding_model="public-bge-m3", dimension=1024):
    """
    创建带标签字段的知识库集合。
    :param ns: 命名空间对象
    :param name: 知识库名称
    :param description: 知识库描述（默认："test"）
    :param embedding_model: 嵌入模型名称（默认："public-bge-m3"）
    :param dimension: 嵌入维度（默认：1024）
    :return: 创建的知识库集合对象
    """
    module__field_info = CollectionFieldInfo(fieldName="module", fieldType="string")
    col = ns.create_collection(name, description, embedding_model=embedding_model,
                                dimension=dimension, field_list=[module__field_info])
    return col


def get_unique_modules(data_list):
    """
    从数据列表中提取用例目录的第二层目录，并返回去重后的列表。
    :param data_list: 包含 row_data 字典的列表
    :return: 去重后的第二层目录列表
    """
    modules = set()
    for row_data in data_list:
        if '用例目录' in row_data:
            parts = row_data['用例目录'].split('/')
            if len(parts) > 1:
                modules.add(parts[1])
    return list(modules)


def get_test_case_secondary_dirs(file_path):
    """
    从用例文件中获取测试用例的二级目录列表。
    :param file_path: 用例文件的路径（Excel 文件）
    :return: 去重后的二级目录列表
    """
    # 读取 Excel 文件并解析数据
    workbook = openpyxl.load_workbook(file_path)
    sheet = workbook.active
    headers = [
        "用例ID", "UUID", "用例标题", "用例目录", "用例描述", "用例负责人",
        "用例类型", "是否自动化", "等级", "前置条件", "步骤描述类型",
        "步骤描述", "预期结果", "评估工时", "标签", "关联需求",
        "关联的自动化用例", "执行次数", "创建人", "创建时间"
    ]
    data_list = []
    for row in sheet.iter_rows(min_row=2, values_only=True):
        row_data = {header: row[i] if row[i] is not None else "" for i, header in enumerate(headers)}
        data_list.append(row_data)
    workbook.close()

    # 调用 _get_unique_modules 方法获取二级目录列表
    return get_unique_modules(data_list)

if __name__ == "__main__":
    # # 创建命名空间
    # rag = TRAG.from_api_key(api_key=os.getenv("TRAG_TOKEN"))
    # # ns = rag.create_namespace("医典", description="")
    # # print(ns.info)

    # # 选择命名空间
    # ns = rag.namespace("ns-6f8495c1")
    # 创建带过滤标签的知识库
    
    # 若无知识库，则创建
    # 创建带过滤标签的知识库
    rag = TRAG.from_api_key(api_key=os.getenv("TRAG_TOKEN"))
    ns = rag.namespace(os.getenv("SAAS_NAMESPACE"))
    coll = create_field_coll(ns,"story","存放 SAAS 业务需求文档",embedding_model="public-bge-m3",dimension=1024)
    print(coll.info)

    # # 创建不带过滤标签的知识库
    # coll = create_coll(ns,"Saas_module", "带模块目录的 Saas用例知识库", "public-bge-m3", 1024)
    # print(coll.info)

    # 初始化 TRAG,命名空间和知识库 id
    # manager = TRAGManager(ns_code=os.getenv("SAAS_NAMESPACE"),coll_code=os.getenv("SAAS_MODULE_CASE_ALL_COLL"))
    # 导入用例
    # manager.import_test_cases("../coll_data/NGES-召回测试用例集-20250611.xlsx")
    # 导入目录
    # manager.import_case_directories("../coll_data/NGES-召回测试用例集-20250611.xlsx")

    # # 清空知识库
    # manager.clean_documents()

    # #检索
    # filter_expr = 'module in ("健康管理助手")'
    # query="{\"name\": \"人物大图更新\", \"description\": \"针对人物大图进行优化更新，主要更新小孩人物图片，确保人物区分更明显，但不影响器官位置展示。\", \"关联需求\": \"【产品需求】健康管理助手-人物素材更新\", \"模块\": [\"健康管理助手\"]}"
    # case_search = manager.search_test_cases(query=query, limit=10,filter_expr=filter_expr)
    # for case in case_search:
    #     print(case)

    # 创建需求文档
    # 选择知识库
    # manager = TRAGManager(ns_code=os.getenv("HEALTH_NAMESPACE"),coll_code=os.getenv("HEALTH_CHECKLIST_CASE_COLL"))
    # manager.clean_documents()
    # 导入
    # manager.import_test_cases("../data/health_checklist.xlsx")
    # # 检索
    # case_search = manager.search_test_cases(query="关于数据中心", limit=10)
    # print(case_search)

    # # 需求知识库全流程
    # manager = TRAGManager(coll_code=os.getenv("TRAG_STORY_COLL"))
    # urls = [
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472123851503?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122077437?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122596893?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122425098?from_iteration_id=1020375472002148335",
    # ]
    # manager.import_require(urls)
    # require_search = manager.search_test_cases(query="关于咨询人的需求", limit=1)
    # print(require_search)
