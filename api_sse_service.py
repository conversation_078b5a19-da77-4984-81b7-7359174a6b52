import json
import os
from fastapi import HTTPException
from flask import Flask, Response, request
import time
from openai import OpenAI
from rag_server.recall_case import ReCallCase
from tools.recall_case_tool import create_response, extract_https_links
from utils.logger.logger import Logging
from dotenv import load_dotenv

logger = Logging().get_logger()
hy_client = OpenAI(base_url=os.getenv('hunyuan_url'), api_key=os.getenv('hunyuan_t1_key'))
load_dotenv()
app = Flask(__name__)
@app.post('/medical_case_recall')
def sse_stream():
    try:
        body = request.json
        story_url = body.get("story_url")
        story_content = body.get("story_content")
    except Exception:
        raise HTTPException(400, "story_url参数必填")
    logger.info(f"测试堂传递的需求描述 需求url:{story_url} ,需求信息:{story_content},")
    recall_client = ReCallCase(story_url=story_url)
    def generate():
        module_id = str(time.time())
        # 构造模块信息并推送
        module_params = {
            "data": [
                {
                    "id": module_id,
                    "content": "回归用例",
                    "parent_id": "",
                    "node_type": "FEATURE"
                }
            ]
        }
        module_params_str = json.dumps(module_params, ensure_ascii=False)
        yield f"data: {module_params_str}\n\n"
        for core in recall_client.cores:
            case = recall_client.gen_regress_case(core, is_zhiyan=True, module_id=module_id).get("result", [])
            if len(case) == 0:
                continue
            return_data = json.dumps({"data": case},ensure_ascii=False)
            # 构造 SSE 格式的数据块
            data = f"data: {return_data}\n\n"
            yield data
    # 设置响应头
    return Response(
        generate(),
        mimetype='text/event-stream',
        headers={
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'X-Accel-Buffering': 'no'  # 禁用缓冲（关键）
        }
    )
@app.post('/medical_case_recall_evalution')
def recall_case_for_evalution():
    try:
        body = request.json
        story_url = body.get("story_url")
    except Exception:
        raise HTTPException(400, "story_url参数必填")

    https_links = extract_https_links(story_url)
    if len(https_links) <= 0:
        return create_response(code=0, msg="", data="入参中需要包含需求单链接信息")

    story_url = https_links[0]

    recall_client = ReCallCase(story_url=story_url)
    case_dict = {"result": [], "rag_doc": [], "rerank_doc": [], "query":[]}
    for core in recall_client.cores:
        dict = recall_client.gen_regress_case(core)
        case_dict["result"].extend(dict["result"])
        case_dict["rag_doc"].extend(dict["rag_doc"])
        case_dict["rerank_doc"].extend(dict["rerank_doc"])
        case_dict["query"].extend(dict["query"])

    return create_response(code=0, msg="", data=case_dict)

if __name__ == '__main__':
    app.run(debug=True, threaded=True, host='0.0.0.0', port=5000)
